# 网站使用说明

## 🎯 快速开始
1. 双击打开 `index.html` 文件即可在浏览器中查看网站
2. 网站会根据你的本地时间自动切换黑白主题：
   - 6:00-18:00：白色背景
   - 18:00-6:00：黑色背景

## 🐍 Python 管理工具（推荐）

### 方法一：使用完整管理工具
```bash
python edit.py
```
提供完整的菜单系统，可以：
- 查看当前视频列表
- 修改现有视频信息
- 添加新视频
- 查看可用视频文件
- 智能文件查找功能

**使用示例：**
```
🎬 视频编号 (建议: 1): 1
📁 视频文件: 1                    ← 只输入数字即可
📝 标题: 我的精彩视频
```

## 🎬 视频功能特性
- **仅支持视频**：网站专门为视频展示设计，不再支持图片
- **智能文件查找**：输入数字自动查找 `videos/` 目录下的文件
- **苹果原生播放器**：完全支持 iOS Safari 和 macOS Safari 原生播放器
- **保持原色**：视频保持原始色彩，不应用滤镜
- **响应式设计**：
  - 手机端：一行一个视频
  - iPad端：一行两个视频
  - 电脑端：一行三个视频

## 📱 响应式布局
- **手机端**：一行显示 1 个视频，更好的观看体验
- **iPad端**：一行显示 2 个视频
- **电脑端**：一行显示 3 个视频
- **所有设备**：保持16:9宽高比

## 🎥 支持的视频格式
- MP4, MOV, AVI, MKV, WebM, M4V, 3GP
- 建议使用 MP4 格式以获得最佳兼容性

## 💡 智能文件管理
### 文件命名方式
- 推荐命名：`1.mp4`, `2.mp4`, `3.mp4` ...
- 也支持：`myvideo.mp4`, `vacation.mov` 等

### 输入方式
- **数字输入**：输入 `1` 自动查找 `videos/1.mp4`
- **文件名输入**：输入 `myvideo` 自动查找 `videos/myvideo.mp4`
- **完整路径**：输入 `videos/special.mp4`

## 🎨 设计特色
- 纯黑白配色的界面设计
- 等宽字体增强技术感
- 视频保持原始色彩
- 悬停时有优雅的动画效果
- 严格的网格布局系统
- 视频下方显示灰色标题文字
- 使用系统原生播放控件

## 🔧 手动修改（高级）
如果不想使用Python工具，可以手动修改HTML：

### 添加视频
```html
<div class="photo-wrapper">
    <div class="photo-item" data-type="video">
        <video controls preload="metadata" playsinline webkit-playsinline>
            <source src="videos/1.mp4" type="video/mp4">
            您的浏览器不支持视频播放。
        </video>
    </div>
    <div class="photo-title">视频标题</div>
</div>
```

## 🚀 使用流程
1. **准备视频**：将视频文件放入 `videos/` 文件夹
2. **命名建议**：使用简单的数字命名，如 `1.mp4`, `2.mp4`
3. **运行工具**：使用 Python 管理工具进行配置
4. **输入信息**：
   - 选择位置编号
   - 输入文件名（数字即可）
   - 输入视频标题
5. **预览效果**：在浏览器中查看结果

## 💡 小贴士
- **视频比例**：建议使用 16:9 比例视频
- **文件大小**：建议控制在 100MB 以内
- **格式优先级**：MP4 > MOV > 其他格式
- **移动优化**：视频会在移动设备上使用原生播放器
- **智能替换**：工具会优先替换示例内容
- **文件检查**：工具会自动检查文件是否存在

## 📁 文件结构
```
├── index.html          # 主网页文件
├── videos/             # 视频存放文件夹
│   ├── 1.mp4          # 推荐命名方式
│   ├── 2.mp4
│   └── ...
├── 管理照片.py         # 完整管理工具
├── 快速修改.py         # 快速修改工具
├── README.md           # 项目说明
└── 使用说明.md         # 详细使用指南
```

## 🎵 视频播放体验
- **所有设备**：使用系统原生播放器
- **苹果设备**：支持画中画、后台播放等高级功能
- **Android设备**：支持全屏播放和手势控制
- **桌面浏览器**：支持键盘快捷键控制
- **色彩还原**：视频显示原始色彩，提供最佳观看体验 