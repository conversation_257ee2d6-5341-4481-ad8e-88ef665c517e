<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hallo!</title>
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 主体样式 - 根据时间切换主题 */
        body {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            line-height: 1.6;
            transition: all 0.3s ease;
            min-height: 100vh;
            padding: 40px 20px;
        }

        /* 白天主题 */
        body.light {
            background-color: #ffffff;
            color: #000000;
        }

        /* 夜晚主题 */
        body.dark {
            background-color: #000000;
            color: #ffffff;
        }

        /* 容器 */
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* 标题样式 */
        h1 {
            font-size: 3rem;
            font-weight: 300;
            text-align: center;
            margin-bottom: 60px;
            letter-spacing: 0.1em;
            border-bottom: 1px solid currentColor;
            padding-bottom: 20px;
        }

        /* 照片网格容器 */
        .photo-grid {
            display: grid;
            gap: 20px;
            margin-bottom: 40px;
        }

        /* 手机端：一行一个视频 */
        @media (max-width: 768px) {
            .photo-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            h1 {
                font-size: 2rem;
                margin-bottom: 40px;
            }
            
            body {
                padding: 20px 15px;
            }
        }

        /* iPad端：一行两个视频 */
        @media (min-width: 769px) and (max-width: 1024px) {
            .photo-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }
        }

        /* 电脑端：一行三个视频 */
        @media (min-width: 1025px) {
            .photo-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 20px;
            }
        }

        /* 媒体项目样式 */
        .photo-item {
            position: relative;
            overflow: hidden;
            border: 1px solid currentColor;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        /* 保持16:9宽高比 */
        .photo-item::before {
            content: '';
            display: block;
            padding-top: 56.25%; /* 16:9 宽高比 */
        }

        /* 视频样式 */
        .photo-item video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.3s ease;
        }

        /* 视频控件样式 - 黑白主题 */
        .photo-item video::-webkit-media-controls-panel {
            background-color: rgba(0, 0, 0, 0.8);
        }

        body.light .photo-item video::-webkit-media-controls-panel {
            background-color: rgba(255, 255, 255, 0.8);
        }

        .photo-item video::-webkit-media-controls-play-button,
        .photo-item video::-webkit-media-controls-start-playback-button {
            filter: invert(1);
        }

        body.light .photo-item video::-webkit-media-controls-play-button,
        body.light .photo-item video::-webkit-media-controls-start-playback-button {
            filter: invert(0);
        }

        /* 视频标题 */
        .photo-title {
            margin-top: 8px;
            font-size: 0.8rem;
            text-align: center;
            color: #666666;
            letter-spacing: 0.02em;
        }

        /* 悬停效果 */
        .photo-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        body.dark .photo-item:hover {
            box-shadow: 0 8px 16px rgba(255, 255, 255, 0.1);
        }

        .photo-item:hover video {
            transform: scale(1.05);
        }

        /* 时间显示 */
        .time-info {
            position: fixed;
            top: 20px;
            right: 20px;
            font-size: 0.8rem;
            opacity: 0.6;
            letter-spacing: 0.05em;
        }
    </style>
</head>
<body>
    <!-- 时间显示 -->
    <div class="time-info" id="timeInfo"></div>

    <div class="container">
        <!-- 主标题 -->
        <h1>Hallo!</h1>

        <!-- 视频网格 -->
        <div class="photo-grid" id="photoGrid">
            <!-- 示例视频 - 用户可以替换这些 -->
            <div class="photo-wrapper">
                <div class="photo-item" data-type="video">
                    <video controls preload="metadata" playsinline webkit-playsinline>
                        <source src="videos/example1.mp4" type="video/mp4">
                        请添加您的视频文件到 videos/ 文件夹
                    </video>
                </div>
                <div class="photo-title">示例视频 1</div>
            </div>
            <div class="photo-wrapper">
                <div class="photo-item" data-type="video">
                    <video controls preload="metadata" playsinline webkit-playsinline>
                        <source src="videos/example2.mp4" type="video/mp4">
                        请添加您的视频文件到 videos/ 文件夹
                    </video>
                </div>
                <div class="photo-title">示例视频 2</div>
            </div>
            <div class="photo-wrapper">
                <div class="photo-item" data-type="video">
                    <video controls preload="metadata" playsinline webkit-playsinline>
                        <source src="videos/example3.mp4" type="video/mp4">
                        请添加您的视频文件到 videos/ 文件夹
                    </video>
                </div>
                <div class="photo-title">示例视频 3</div>
            </div>
            <div class="photo-wrapper">
                <div class="photo-item" data-type="video">
                    <video controls preload="metadata" playsinline webkit-playsinline>
                        <source src="videos/example4.mp4" type="video/mp4">
                        请添加您的视频文件到 videos/ 文件夹
                    </video>
                </div>
                <div class="photo-title">示例视频 4</div>
            </div>
            <div class="photo-wrapper">
                <div class="photo-item" data-type="video">
                    <video controls preload="metadata" playsinline webkit-playsinline>
                        <source src="videos/example5.mp4" type="video/mp4">
                        请添加您的视频文件到 videos/ 文件夹
                    </video>
                </div>
                <div class="photo-title">示例视频 5</div>
            </div>
            <div class="photo-wrapper">
                <div class="photo-item" data-type="video">
                    <video controls preload="metadata" playsinline webkit-playsinline>
                        <source src="videos/example6.mp4" type="video/mp4">
                        请添加您的视频文件到 videos/ 文件夹
                    </video>
                </div>
                <div class="photo-title">示例视频 6</div>
            </div>
        </div>
    </div>

    <script>
        // 根据时间设置主题
        function setThemeByTime() {
            const now = new Date();
            const hour = now.getHours();
            const body = document.body;
            
            // 6:00-18:00 为白天主题，其他时间为夜晚主题
            if (hour >= 6 && hour < 18) {
                body.className = 'light';
            } else {
                body.className = 'dark';
            }
        }

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('timeInfo').textContent = timeString;
        }

        // 视频播放控制（使用原生播放器）
        function handleVideoClick(event) {
            // 让视频使用原生控件，不阻止默认行为
            return true;
        }

        // 初始化
        function init() {
            setThemeByTime();
            updateTime();
            
            // 每分钟更新一次时间和主题
            setInterval(() => {
                setThemeByTime();
                updateTime();
            }, 60000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html> 