# Hallo! 照片/视频展示网站

## 项目简介
一个纯粹黑白风格的照片/视频展示网站，遵循极简主义设计哲学，支持苹果设备原生播放器。

## 功能特性
- 响应式设计：手机端一行显示两个媒体，iPad和电脑端自适应
- 智能主题：根据访问者本地时间自动切换黑白背景
- 图片点击跳转功能
- 视频纯播放功能：无跳转，专注内容展示
- 视频播放支持：兼容苹果Safari原生播放器
- 纯粹的黑白视觉表达

## 媒体支持
### 图片格式
- JPG, JPEG, PNG, GIF, WebP, BMP, SVG

### 视频格式  
- MP4, MOV, AVI, MKV, WebM, M4V, 3GP
- 支持iOS Safari和macOS Safari原生播放器
- 自动应用黑白滤镜效果
- 16:9比例适配

## 技术栈
- HTML5 (包含video标签)
- CSS3 (Grid布局 + 媒体查询 + 视频样式)
- Vanilla JavaScript (视频播放控制)
- Python 3 (内容管理工具)

## 设计理念
- 纯粹黑白：拒绝彩色，坚持黑白纯净表达
- 线条美学：通过边框和结构创造视觉层次
- 留白艺术：适当留白创造呼吸感
- 网格布局：严格的响应式网格系统
- 媒体统一：图片和视频采用相同的视觉处理

## 使用方法
1. 将照片放入 `images/` 目录，视频放入 `videos/` 目录
2. 使用Python管理工具修改内容：
   - `python 管理照片.py` - 完整管理界面
   - `python 快速修改.py` - 快速修改单个项目
3. 直接在浏览器中打开 `index.html`

## 特色功能
- 🎬 视频播放：纯粹的播放体验，无跳转干扰
- 🔗 图片跳转：点击图片可跳转到指定链接
- 📱 苹果优化：支持playsinline和webkit-playsinline
- 🎨 黑白滤镜：图片和视频统一应用灰度效果
- ⚡ 智能检测：Python工具自动识别文件类型并生成对应功能 