#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hallo! 视频网站管理工具
用于方便地更新视频和标题（仅支持视频）
"""

import re
import os
import sys
import glob

# 支持的视频文件扩展名
VIDEO_EXTENSIONS = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v', '.3gp']

def find_video_file(filename_input):
    """根据输入的文件名（可能不带扩展名）查找视频文件"""
    # 如果已经是完整路径且存在，直接返回
    if os.path.exists(filename_input):
        return filename_input
    
    # 如果是数字，在videos目录中查找
    if filename_input.isdigit():
        for ext in VIDEO_EXTENSIONS:
            video_path = f"videos/{filename_input}{ext}"
            if os.path.exists(video_path):
                return video_path
    
    # 如果是文件名（不带路径），在videos目录中查找
    if not filename_input.startswith('videos/'):
        # 先尝试直接添加videos/前缀
        for ext in VIDEO_EXTENSIONS:
            if filename_input.endswith(ext):
                video_path = f"videos/{filename_input}"
                if os.path.exists(video_path):
                    return video_path
            else:
                video_path = f"videos/{filename_input}{ext}"
                if os.path.exists(video_path):
                    return video_path
    
    # 如果都没找到，返回原输入
    return filename_input

def create_video_html(video_path, title):
    """创建视频HTML结构"""
    return f'''            <div class="photo-wrapper">
                <div class="photo-item" data-type="video">
                    <video controls preload="metadata" playsinline webkit-playsinline>
                        <source src="{video_path}" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                </div>
                <div class="photo-title">{title}</div>
            </div>'''

def read_html_file():
    """读取HTML文件内容"""
    try:
        with open('index.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print("❌ 未找到 index.html 文件！请确保脚本在正确的目录中运行。")
        return None

def write_html_file(content):
    """写入HTML文件"""
    try:
        with open('index.html', 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except Exception as e:
        print(f"❌ 写入文件失败：{e}")
        return False

def list_videos():
    """列出当前所有视频"""
    html_content = read_html_file()
    if not html_content:
        return
    
    print("\n🎬 当前视频列表：")
    print("=" * 60)
    
    # 找到所有video元素
    video_pattern = r'<div class="photo-wrapper">.*?<source src="([^"]*)"[^>]*>.*?<div class="photo-title">([^<]*)</div>'
    matches = re.findall(video_pattern, html_content, re.DOTALL)
    
    for i, (video_path, title) in enumerate(matches, 1):
        status = "✅ 存在" if os.path.exists(video_path) else "❌ 文件不存在"
        print(f"视频 {i}:")
        print(f"  🎬 文件：{video_path} {status}")
        print(f"  📝 标题：{title}")
        print("-" * 40)
    
    return len(matches)

def update_video(html_content, video_num, video_path, title):
    """更新指定位置的视频"""
    new_video_block = create_video_html(video_path, title)
    
    # 查找所有video块
    pattern = r'(<div class="photo-wrapper">.*?</div>\s*</div>)'
    matches = list(re.finditer(pattern, html_content, re.DOTALL))
    
    if video_num > len(matches):
        print(f"❌ 视频 {video_num} 不存在！当前只有 {len(matches)} 个视频。")
        return html_content
    
    # 替换指定的视频块
    start = matches[video_num - 1].start()
    end = matches[video_num - 1].end()
    
    new_content = html_content[:start] + new_video_block + html_content[end:]
    return new_content

def find_first_example_position(html_content):
    """查找第一个示例视频的位置"""
    example_pattern = r'<source src="videos/example\d+\.mp4"'
    match = re.search(example_pattern, html_content)
    
    if match:
        # 计算这是第几个video
        before_match = html_content[:match.start()]
        video_count = len(re.findall(r'<div class="photo-wrapper">', before_match)) + 1
        return video_count
    
    return None

def add_video(html_content, video_path, title):
    """添加新视频，优先替换示例内容"""
    # 查找第一个示例内容的位置
    example_pos = find_first_example_position(html_content)
    
    if example_pos:
        print(f"📍 将替换位置 {example_pos} 的示例内容")
        return update_video(html_content, example_pos, video_path, title)
    else:
        print("📍 在末尾添加新视频")
        new_video_block = create_video_html(video_path, title)
        
        # 在最后添加
        pattern = r'(</div>\s*</div>\s*</div>\s*<script>)'
        replacement = f'{new_video_block}\n        </div>\n    </div>\n\n    <script>'
        
        new_content = re.sub(pattern, replacement, html_content)
        return new_content

def list_available_videos():
    """列出videos文件夹中的所有视频文件"""
    if not os.path.exists('videos'):
        print("❌ videos 文件夹不存在")
        return
    
    video_files = []
    for ext in VIDEO_EXTENSIONS:
        video_files.extend(glob.glob(f'videos/*{ext}'))
    
    if video_files:
        print("\n📁 videos 文件夹中的视频文件：")
        print("-" * 40)
        for i, video_file in enumerate(sorted(video_files), 1):
            filename = os.path.basename(video_file)
            name_without_ext = os.path.splitext(filename)[0]
            size = os.path.getsize(video_file) / (1024*1024)  # MB
            print(f"{name_without_ext} - {filename} ({size:.1f}MB)")
        print("-" * 40)
    else:
        print("\n📁 videos 文件夹中没有找到视频文件")

def main_menu():
    """主菜单"""
    while True:
        print("\n" + "="*50)
        print("🎬 Hallo! 视频网站管理工具")
        print("="*50)
        print("1. 📋 查看当前视频列表")
        print("2. ✏️  修改现有视频")
        print("3. ➕ 添加新视频")
        print("4. 📁 查看可用视频文件")
        print("5. 🚪 退出程序")
        print("-"*50)
        
        choice = input("请选择操作 (1-5): ").strip()
        
        if choice == '1':
            list_videos()
            
        elif choice == '2':
            modify_video()
            
        elif choice == '3':
            add_new_video()
            
        elif choice == '4':
            list_available_videos()
            
        elif choice == '5':
            print("👋 再见！")
            break
            
        else:
            print("❌ 无效选择，请输入 1-5")

def modify_video():
    """修改现有视频"""
    html_content = read_html_file()
    if not html_content:
        return
    
    video_count = list_videos()
    if video_count == 0:
        print("没有视频可以修改")
        return
    
    try:
        video_num = int(input(f"\n请输入要修改的视频编号 (1-{video_count}): "))
    except ValueError:
        print("❌ 请输入有效的数字")
        return
    
    if video_num < 1 or video_num > video_count:
        print(f"❌ 视频编号必须在 1-{video_count} 之间")
        return
    
    print(f"\n正在修改视频 {video_num}...")
    print("💡 提示：直接按回车跳过不修改的项目")
    
    # 获取当前信息
    video_pattern = r'<div class="photo-wrapper">.*?<source src="([^"]*)"[^>]*>.*?<div class="photo-title">([^<]*)</div>'
    matches = re.findall(video_pattern, html_content, re.DOTALL)
    
    current_video, current_title = matches[video_num - 1]
    
    print(f"\n当前信息：")
    print(f"🎬 文件：{current_video}")
    print(f"📝 标题：{current_title}")
    
    # 获取新信息
    new_video_input = input(f"\n新的视频文件 (数字或文件名，当前: {current_video}): ").strip()
    if new_video_input:
        new_video = find_video_file(new_video_input)
        if not os.path.exists(new_video):
            print(f"⚠️  警告：文件 {new_video} 不存在")
    else:
        new_video = current_video
    
    new_title = input(f"新的标题 (当前: {current_title}): ").strip()
    if not new_title:
        new_title = current_title
    
    # 更新HTML
    updated_content = update_video(html_content, video_num, new_video, new_title)
    
    if write_html_file(updated_content):
        print("✅ 视频信息更新成功！")
        print(f"🎬 文件: {new_video}")
        print(f"📝 标题: {new_title}")
    else:
        print("❌ 更新失败")

def add_new_video():
    """添加新视频"""
    html_content = read_html_file()
    if not html_content:
        return
    
    print("\n➕ 添加新视频")
    print("-" * 30)
    
    list_available_videos()
    
    video_input = input("\n视频文件 (输入数字或文件名，如 '1' 或 'myvideo'): ").strip()
    if not video_input:
        print("❌ 视频文件不能为空")
        return
    
    video_path = find_video_file(video_input)
    if not os.path.exists(video_path):
        print(f"⚠️  警告：文件 {video_path} 不存在，但仍会添加到HTML中")
    
    title = input("视频标题: ").strip()
    if not title:
        print("❌ 视频标题不能为空")
        return
    
    print(f"\n📋 添加信息确认：")
    print(f"🎬 文件：{video_path}")
    print(f"📝 标题：{title}")
    
    confirm = input("\n确认添加？(y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 已取消添加")
        return
    
    # 添加视频
    updated_content = add_video(html_content, video_path, title)
    
    if write_html_file(updated_content):
        print("✅ 新视频添加成功！")
    else:
        print("❌ 添加失败")

if __name__ == "__main__":
    print("🎬 Hallo! 视频网站管理工具")
    print("📁 请确保此脚本与 index.html 在同一目录下")
    print("🎥 仅支持视频格式：MP4, MOV, AVI, MKV, WebM, M4V, 3GP")
    print("💡 输入数字自动查找 videos/ 目录下的文件")
    
    if not os.path.exists('index.html'):
        print("❌ 未找到 index.html 文件！")
        sys.exit(1)
    
    # 确保videos目录存在
    if not os.path.exists('videos'):
        os.makedirs('videos')
        print("📁 已创建 videos 文件夹")
    
    main_menu() 